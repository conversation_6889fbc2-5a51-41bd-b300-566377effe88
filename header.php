<?php
/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @package Dakoii_Provincial_Government_Theme
 */
?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="profile" href="https://gmpg.org/xfn/11">
    
    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>
<?php wp_body_open(); ?>

<a class="skip-link screen-reader-text" href="#content"><?php esc_html_e('Skip to content', 'dakoii-provincial-government-theme'); ?></a>

<!-- PNG National Emblem -->
<?php if (nols_espa_show_png_emblem()) : ?>
<section class="national-emblem">
    <div class="emblem-container">
        <?php $emblem_image = nols_espa_get_png_emblem_image(); ?>
        <div class="png-emblem<?php echo $emblem_image ? ' has-custom-image' : ''; ?>">
            <?php if ($emblem_image) : ?>
                <img src="<?php echo esc_url($emblem_image); ?>" alt="<?php echo esc_attr(nols_espa_get_png_emblem_text()); ?>" class="png-emblem-image">
            <?php endif; ?>
        </div>
        <div class="emblem-text"><?php echo esc_html(nols_espa_get_png_emblem_text()); ?></div>
    </div>
</section>
<?php endif; ?>

<!-- Header -->
<header class="site-header">
    <div class="header-content">
        <div class="gov-logo">
            <?php if (has_custom_logo()) : ?>
                <div class="custom-logo">
                    <?php the_custom_logo(); ?>
                </div>
            <?php else : ?>
                <div class="logo-shield"></div>
            <?php endif; ?>

            <div class="site-branding">
                <?php if (is_front_page() && is_home()) : ?>
                    <h1 class="gov-title">
                        <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                            <?php
                            $site_name = get_bloginfo('name');
                            if (empty($site_name)) {
                                esc_html_e('East Sepik Provincial Administration', 'dakoii-provincial-government-theme');
                            } else {
                                echo esc_html($site_name);
                            }
                            ?>
                        </a>
                    </h1>
                <?php else : ?>
                    <p class="gov-title">
                        <a href="<?php echo esc_url(home_url('/')); ?>" rel="home">
                            <?php
                            $site_name = get_bloginfo('name');
                            if (empty($site_name)) {
                                esc_html_e('East Sepik Provincial Administration', 'dakoii-provincial-government-theme');
                            } else {
                                echo esc_html($site_name);
                            }
                            ?>
                        </a>
                    </p>
                <?php endif; ?>

                <?php
                $description = get_bloginfo('description', 'display');
                if ($description || is_customize_preview()) :
                ?>
                    <p class="gov-subtitle"><?php echo $description ? $description : esc_html__('Papua New Guinea Government', 'dakoii-provincial-government-theme'); ?></p>
                <?php else : ?>
                    <p class="gov-subtitle"><?php esc_html_e('Papua New Guinea Government', 'dakoii-provincial-government-theme'); ?></p>
                <?php endif; ?>
            </div>
        </div>

        <div class="emergency-contact">
            <div><?php esc_html_e('Emergency Services', 'dakoii-provincial-government-theme'); ?></div>
            <div class="emergency-number">000</div>
        </div>

        <?php if (has_nav_menu('primary')) : ?>
            <button class="menu-toggle" aria-controls="primary-menu" aria-expanded="false">
                <span class="screen-reader-text"><?php esc_html_e('Primary Menu', 'dakoii-provincial-government-theme'); ?></span>
                <span class="menu-icon">☰</span>
            </button>
        <?php endif; ?>
    </div>
</header>

<!-- Navigation -->
<?php if (has_nav_menu('primary')) : ?>
<nav class="main-nav" id="site-navigation" aria-label="<?php esc_attr_e('Primary menu', 'dakoii-provincial-government-theme'); ?>">
    <div class="nav-container">
        <?php
        wp_nav_menu(array(
            'theme_location' => 'primary',
            'menu_id'        => 'primary-menu',
            'menu_class'     => 'nav-menu',
            'container'      => false,
            'fallback_cb'    => 'nols_espa_theme_fallback_menu',
        ));
        ?>
    </div>
</nav>
<?php else : ?>
<nav class="main-nav" id="site-navigation" aria-label="<?php esc_attr_e('Primary menu', 'dakoii-provincial-government-theme'); ?>">
    <div class="nav-container">
        <?php nols_espa_theme_fallback_menu(); ?>
    </div>
</nav>
<?php endif; ?>

<div id="content" class="site-content">

<?php
/**
 * Fallback menu when no menu is assigned
 */
function nols_espa_theme_fallback_menu() {
    $current_page_id = get_queried_object_id();
    ?>
    <ul class="nav-menu">
        <li class="nav-item <?php echo (is_front_page()) ? 'current_page_item' : ''; ?>">
            <a href="<?php echo esc_url(home_url('/')); ?>"><?php esc_html_e('Home', 'dakoii-provincial-government-theme'); ?></a>
        </li>
        <?php
        // Get pages for menu
        $pages = get_pages(array(
            'sort_order' => 'ASC',
            'sort_column' => 'menu_order',
            'number' => 5,
        ));

        foreach ($pages as $page) :
            $is_current = ($current_page_id == $page->ID);
        ?>
            <li class="nav-item <?php echo $is_current ? 'current_page_item' : ''; ?>">
                <a href="<?php echo esc_url(get_permalink($page->ID)); ?>">
                    <?php echo esc_html($page->post_title); ?>
                </a>
            </li>
        <?php endforeach; ?>
        
        <?php if (get_option('show_on_front') == 'posts') :
            $blog_page_id = get_option('page_for_posts');
            $is_blog_current = (is_home() || (is_single() && get_post_type() == 'post') || ($current_page_id == $blog_page_id));
        ?>
            <li class="nav-item <?php echo $is_blog_current ? 'current_page_item' : ''; ?>">
                <a href="<?php echo esc_url(get_permalink($blog_page_id)); ?>">
                    <?php esc_html_e('Blog', 'dakoii-provincial-government-theme'); ?>
                </a>
            </li>
        <?php endif; ?>

        <?php
        // Add categories to menu
        $categories = get_categories(array(
            'orderby' => 'name',
            'order'   => 'ASC',
            'number'  => 3,
        ));

        $current_category = get_queried_object();
        foreach ($categories as $category) :
            $is_category_current = (is_category() && isset($current_category->term_id) && $current_category->term_id == $category->term_id);
        ?>
            <li class="nav-item <?php echo $is_category_current ? 'current_page_item' : ''; ?>">
                <a href="<?php echo esc_url(get_category_link($category->term_id)); ?>">
                    <?php echo esc_html($category->name); ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
    <?php
}
?>
