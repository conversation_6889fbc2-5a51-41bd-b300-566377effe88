/**
 * Customizer Live Preview for PNG Emblem
 *
 * @package Dakoii_Provincial_Government_Theme
 */

(function($) {
    'use strict';

    // PNG Emblem Image
    wp.customize('png_emblem_image', function(value) {
        value.bind(function(newval) {
            var $emblem = $('.png-emblem');
            var $emblemImage = $('.png-emblem-image');
            
            if (newval) {
                // Get the image URL from the attachment ID
                wp.media.attachment(newval).fetch().then(function() {
                    var imageUrl = wp.media.attachment(newval).get('url');
                    
                    if ($emblemImage.length) {
                        $emblemImage.attr('src', imageUrl);
                    } else {
                        $emblem.append('<img src="' + imageUrl + '" alt="PNG Emblem" class="png-emblem-image">');
                    }
                    
                    $emblem.addClass('has-custom-image');
                });
            } else {
                $emblemImage.remove();
                $emblem.removeClass('has-custom-image');
            }
        });
    });

    // PNG Emblem Text
    wp.customize('png_emblem_text', function(value) {
        value.bind(function(newval) {
            $('.emblem-text').text(newval);
        });
    });

    // Show/Hide PNG Emblem
    wp.customize('show_png_emblem', function(value) {
        value.bind(function(newval) {
            if (newval) {
                $('.national-emblem').show();
            } else {
                $('.national-emblem').hide();
            }
        });
    });

    // PNG Colors (existing functionality enhancement)
    wp.customize('png_red_color', function(value) {
        value.bind(function(newval) {
            $('head').find('#png-red-color').remove();
            $('head').append('<style id="png-red-color">:root { --png-red: ' + newval + '; }</style>');
        });
    });

    wp.customize('png_green_color', function(value) {
        value.bind(function(newval) {
            $('head').find('#png-green-color').remove();
            $('head').append('<style id="png-green-color">:root { --png-green: ' + newval + '; }</style>');
        });
    });

    wp.customize('png_yellow_color', function(value) {
        value.bind(function(newval) {
            $('head').find('#png-yellow-color').remove();
            $('head').append('<style id="png-yellow-color">:root { --png-yellow: ' + newval + '; }</style>');
        });
    });

})(jQuery);
